<script lang='ts' setup>
import { initModule } from './router.ts'
import { onMounted, ref } from 'vue';
import VoiceSelection from './components/VoiceSelection.vue';
import VoiceSynthesis from './components/VoiceSynthesis.vue';
const currentVoice = ref<any>()
onMounted(() => {
  initModule();
})
const selectVoice = (item: any) => {
  currentVoice.value = item
}

</script>

<template>
  <div class="text-to-speech">
    <VoiceSelection @selectVoice="selectVoice"></VoiceSelection>
    <VoiceSynthesis :currentVoice="currentVoice"></VoiceSynthesis>
  </div>
</template>

<style lang="scss">
.module-container {
  padding: 0 !important;
  margin: 0 !important;
}

:root .text-to-speech{
  --page-bg-color: #fbfbfb;
  --page-bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #2882F0;
  --card-bg: white;
  --text-color: #656d74;
  --border-color: #ebebeb;
  --hover-bg: #f5f5f5;
  --active-bg: #edf6ff;
  --active-border: #0383f8;
  --active-text: #0383f8;
  --textarea-bg: #f2f2f2;
  --player-btg: #edf6ff;
}

:root.dark .text-to-speech{
  --page-bg-color: #0f172a;
  --page-bg-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  --primary-color: #177ddc;
  --card-bg: #0d1320;
  --text-color: #fff;
  --border-color: #ffffff14;
  --hover-bg: #69696929;
  --active-bg: #1a2332;
  --active-border: #177ddc;
  --active-text: #4da6ff;
  --textarea-bg: #f2f2f217;
  --player-btg: #f2f2f217;

}

.text-to-speech {
  width: 100%;
  height: calc(100vh - 64px);
  background: var(--page-bg-color) no-repeat;
  background-size: cover;
  padding: 32px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .page-header {
    text-align: center;
    margin-bottom: 48px;
    padding: 32px 0;
    background: var(--page-bg-gradient);
    border-radius: 20px;
    color: white;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);

    .page-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      font-size: 32px;
      font-weight: 700;
      margin: 0 0 12px 0;

      .title-icon {
        width: 40px;
        height: 40px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }
    }

    .page-subtitle {
      font-size: 18px;
      font-weight: 400;
      margin: 0;
      opacity: 0.9;
    }
  }
}

@media (max-width: 768px) {
  .text-to-speech {
    padding: 16px;

    .page-header {
      margin-bottom: 32px;
      padding: 24px 16px;

      .page-title {
        font-size: 24px;

        .title-icon {
          width: 32px;
          height: 32px;
        }
      }

      .page-subtitle {
        font-size: 16px;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    /* 完全透明 */
  }

  to {
    opacity: 1;
    /* 完全不透明 */
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  padding: 100px 0;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #0000001a;
  border-radius: 3px;
}
</style>
