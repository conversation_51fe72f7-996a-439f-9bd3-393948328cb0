<template>
    <div class="button-com"  :style="{width:width}" >
        <div class="button" :style="{height:height}" v-if="type=='other'">
            <slot></slot>
        </div>
        <div class="button" v-else @click="onClick">
            <img v-if="type == 'loading'" class="loading" src="../assets/loading.svg" alt="">
            <img v-if="type == 'voice'" src="../assets/voice.svg" alt="">
            <img v-if="type == 'costumize'" :src="src" alt="">
            <span>{{ type == 'loading' ? loadContent : content }}</span>
        </div>
        
    </div>
</template>
<script lang="ts" setup>
defineProps({
    type: {
        type: String,
        default: 'voice'
    },
    src: {
        type: String,
        default: ''
    },
    content: {
        type: String,
        default: '开始合成'
    }, 
    loadContent: {
        type: String,
        default: '合成中...'
    },
    width: {
        type: String,
        default: '100%'
    },
    height:{
        type:String,
        default: '45px'
    }
})

const emit = defineEmits(['click'])

const onClick = () => {
    emit('click')
}
</script>
<style lang="scss" scoped>
.button {
    width: 100%;
    min-width: 140px;
    height: 45px;
    color: white;
    background: linear-gradient(270deg, #00B6FF 0%, #0085F9 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 28px;
    box-shadow: 0px 10px 20px 0px #00b6ff66;
    cursor: pointer;
    transition: all .3s cubic-bezier(.175, .885, .32, 1.275);
    flex-shrink: 0;
    animation: fadeIn 0.2s ease-in forwards;

    span {
        margin-left: 10px;
    }

    img {
        width: 30px;
        height: 30px;
    }
    .voice{
                width: 30px;
        height: 30px;
    }
    img.loading {
        width: 24px;
        height: 24px;
        animation: rotate_an 1.2s linear infinite;
        transform-origin: center;
    }

    @keyframes rotate_an {
        from {
            transform: rotate(0deg)
        }

        to {
            transform: rotate(360deg)
        }
    }
}

.button:hover {
    transform: translateY(-2px) scale(1.008);
}

.button:active {
    transform: scale(0.96);
}

.button:disabled {
    display: none;
}
</style>