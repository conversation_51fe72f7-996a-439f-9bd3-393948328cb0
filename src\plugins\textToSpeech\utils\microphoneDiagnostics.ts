/**
 * 麦克风诊断工具
 * 专门用于诊断和解决 NotReadableError 等麦克风访问问题
 */

import { getBrowserInfo } from './utils';

export interface DiagnosticResult {
    category: string;
    status: 'success' | 'warning' | 'error' | 'info';
    message: string;
    details?: string;
    suggestions?: string[];
}

export interface MicrophoneDiagnostics {
    browserInfo: any;
    results: DiagnosticResult[];
    overallStatus: 'healthy' | 'warning' | 'critical';
    recommendations: string[];
}

/**
 * 运行完整的麦克风诊断
 */
export const runMicrophoneDiagnostics = async (): Promise<MicrophoneDiagnostics> => {
    const browserInfo = getBrowserInfo();
    const results: DiagnosticResult[] = [];
    
    // 1. 检查基础API支持
    results.push(await checkBasicAPISupport());
    
    // 2. 检查HTTPS状态
    results.push(checkHTTPSStatus());
    
    // 3. 检查权限状态
    results.push(await checkPermissionStatus());
    
    // 4. 检查音频设备
    results.push(await checkAudioDevices());
    
    // 5. 检查AudioContext
    results.push(await checkAudioContext());
    
    // 6. 检查MediaRecorder支持
    results.push(checkMediaRecorderSupport());
    
    // 7. 尝试基础音频访问
    results.push(await testBasicAudioAccess());
    
    // 分析整体状态
    const errorCount = results.filter(r => r.status === 'error').length;
    const warningCount = results.filter(r => r.status === 'warning').length;
    
    let overallStatus: 'healthy' | 'warning' | 'critical';
    if (errorCount > 0) {
        overallStatus = 'critical';
    } else if (warningCount > 0) {
        overallStatus = 'warning';
    } else {
        overallStatus = 'healthy';
    }
    
    // 生成推荐方案
    const recommendations = generateRecommendations(results, browserInfo);
    
    return {
        browserInfo,
        results,
        overallStatus,
        recommendations
    };
};

/**
 * 检查基础API支持
 */
const checkBasicAPISupport = async (): Promise<DiagnosticResult> => {
    if (!navigator.mediaDevices) {
        return {
            category: 'API支持',
            status: 'error',
            message: 'MediaDevices API 不支持',
            suggestions: ['使用现代浏览器', '检查浏览器版本']
        };
    }
    
    if (!navigator.mediaDevices.getUserMedia) {
        return {
            category: 'API支持',
            status: 'error',
            message: 'getUserMedia API 不支持',
            suggestions: ['更新浏览器到最新版本']
        };
    }
    
    return {
        category: 'API支持',
        status: 'success',
        message: '基础API支持正常'
    };
};

/**
 * 检查HTTPS状态
 */
const checkHTTPSStatus = (): DiagnosticResult => {
    const isHTTPS = location.protocol === 'https:';
    const isLocalhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
    const isFile = location.protocol === 'file:';
    
    if (isHTTPS || isLocalhost || isFile) {
        return {
            category: 'HTTPS状态',
            status: 'success',
            message: '安全上下文正常',
            details: `协议: ${location.protocol}, 主机: ${location.hostname}`
        };
    }
    
    return {
        category: 'HTTPS状态',
        status: 'error',
        message: '需要HTTPS或localhost环境',
        details: `当前: ${location.protocol}//${location.hostname}`,
        suggestions: ['使用HTTPS协议', '在localhost环境下测试']
    };
};

/**
 * 检查权限状态
 */
const checkPermissionStatus = async (): Promise<DiagnosticResult> => {
    try {
        if (!navigator.permissions) {
            return {
                category: '权限检查',
                status: 'warning',
                message: '权限API不支持，无法检查权限状态'
            };
        }
        
        const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
        
        switch (permission.state) {
            case 'granted':
                return {
                    category: '权限检查',
                    status: 'success',
                    message: '麦克风权限已授予'
                };
            case 'denied':
                return {
                    category: '权限检查',
                    status: 'error',
                    message: '麦克风权限被拒绝',
                    suggestions: ['在浏览器设置中允许麦克风访问', '重新加载页面并授权']
                };
            case 'prompt':
                return {
                    category: '权限检查',
                    status: 'warning',
                    message: '需要用户授权麦克风权限'
                };
            default:
                return {
                    category: '权限检查',
                    status: 'warning',
                    message: `未知权限状态: ${permission.state}`
                };
        }
    } catch (error: any) {
        return {
            category: '权限检查',
            status: 'error',
            message: '权限检查失败',
            details: error.message
        };
    }
};

/**
 * 检查音频设备
 */
const checkAudioDevices = async (): Promise<DiagnosticResult> => {
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        
        if (audioInputs.length === 0) {
            return {
                category: '音频设备',
                status: 'error',
                message: '未检测到音频输入设备',
                suggestions: ['连接麦克风设备', '检查设备驱动程序']
            };
        }
        
        const hasLabels = audioInputs.some(device => device.label);
        
        return {
            category: '音频设备',
            status: 'success',
            message: `检测到 ${audioInputs.length} 个音频输入设备`,
            details: hasLabels ? '设备标签可见' : '设备标签不可见（可能需要权限）'
        };
    } catch (error: any) {
        return {
            category: '音频设备',
            status: 'error',
            message: '设备枚举失败',
            details: error.message
        };
    }
};

/**
 * 检查AudioContext
 */
const checkAudioContext = async (): Promise<DiagnosticResult> => {
    try {
        const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
        
        if (!AudioContextClass) {
            return {
                category: 'AudioContext',
                status: 'error',
                message: 'AudioContext不支持'
            };
        }
        
        const audioContext = new AudioContextClass();
        const result = {
            category: 'AudioContext',
            status: 'success' as const,
            message: 'AudioContext创建成功',
            details: `状态: ${audioContext.state}, 采样率: ${audioContext.sampleRate}Hz`
        };
        
        await audioContext.close();
        return result;
    } catch (error: any) {
        return {
            category: 'AudioContext',
            status: 'error',
            message: 'AudioContext创建失败',
            details: error.message
        };
    }
};

/**
 * 检查MediaRecorder支持
 */
const checkMediaRecorderSupport = (): DiagnosticResult => {
    if (!window.MediaRecorder) {
        return {
            category: 'MediaRecorder',
            status: 'error',
            message: 'MediaRecorder不支持'
        };
    }
    
    const formats = ['audio/wav', 'audio/webm', 'audio/mp4', 'audio/ogg'];
    const supportedFormats = formats.filter(format => MediaRecorder.isTypeSupported(format));
    
    if (supportedFormats.length === 0) {
        return {
            category: 'MediaRecorder',
            status: 'warning',
            message: '没有支持的录音格式'
        };
    }
    
    return {
        category: 'MediaRecorder',
        status: 'success',
        message: `支持 ${supportedFormats.length} 种录音格式`,
        details: supportedFormats.join(', ')
    };
};

/**
 * 测试基础音频访问
 */
const testBasicAudioAccess = async (): Promise<DiagnosticResult> => {
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        stream.getTracks().forEach(track => track.stop());
        
        return {
            category: '音频访问测试',
            status: 'success',
            message: '基础音频访问成功'
        };
    } catch (error: any) {
        let suggestions: string[] = [];
        
        switch (error.name) {
            case 'NotReadableError':
                suggestions = [
                    '关闭其他正在使用麦克风的应用程序',
                    '重启浏览器',
                    '检查音频驱动程序',
                    '在设备管理器中重新启用音频设备'
                ];
                break;
            case 'NotAllowedError':
                suggestions = ['允许浏览器访问麦克风权限'];
                break;
            case 'NotFoundError':
                suggestions = ['连接麦克风设备', '检查设备驱动'];
                break;
        }
        
        return {
            category: '音频访问测试',
            status: 'error',
            message: `音频访问失败: ${error.name}`,
            details: error.message,
            suggestions
        };
    }
};

/**
 * 生成推荐方案
 */
const generateRecommendations = (results: DiagnosticResult[], browserInfo: any): string[] => {
    const recommendations: string[] = [];
    const hasNotReadableError = results.some(r => r.details?.includes('NotReadableError'));
    
    if (hasNotReadableError) {
        recommendations.push('🔧 NotReadableError 专项解决方案：');
        recommendations.push('1. 关闭所有可能使用麦克风的应用程序（QQ、微信、钉钉、腾讯会议、Discord等）');
        recommendations.push('2. 重启浏览器');
        recommendations.push('3. 检查Windows隐私设置中的麦克风权限');
        recommendations.push('4. 在设备管理器中禁用并重新启用麦克风设备');
        recommendations.push('5. 更新音频驱动程序');
        
        if (browserInfo.isEdge) {
            recommendations.push('6. Edge浏览器特殊建议：尝试使用Chrome浏览器');
        }
    }
    
    // 添加其他推荐
    const errorResults = results.filter(r => r.status === 'error');
    errorResults.forEach(result => {
        if (result.suggestions) {
            recommendations.push(...result.suggestions.map(s => `• ${s}`));
        }
    });
    
    if (recommendations.length === 0) {
        recommendations.push('✅ 系统状态良好，无需特殊操作');
    }
    
    return [...new Set(recommendations)]; // 去重
};
