export const detectLanguage = (inputText: string | undefined) => {
    if (!inputText) return { lang: '', text: '', counts: { zh: 0, ug: 0, en: 0, other: 0 }, dir: 'ltr' }; // Added dir here

    // 中文Unicode范围
    const chineseRanges = [
        [0x4E00, 0x9FFF],   // 基本汉字
        [0x3400, 0x4DBF],   // 扩展A
        [0x20000, 0x2A6DF], // 扩展B
    ];

    // 维吾尔语（阿拉伯字母）Unicode范围
    const uyghurRanges = [
        [0x0600, 0x06FF],   // 基本阿拉伯字母
        [0x0750, 0x077F],   // 阿拉伯字母补充
        [0x08A0, 0x08FF],   // 阿拉伯字母扩展-A
    ];

    // 英语（拉丁字母）Unicode范围
    const englishRanges = [
        [0x0041, 0x005A], // A-Z
        [0x0061, 0x007A], // a-z
    ];

    let zh = 0, ug = 0, en = 0, other = 0;

    for (const char of inputText) {
        const code: number = char.codePointAt(0) || 0;
        let matched = false;

        // 检查中文
        for (const [start, end] of chineseRanges) {
            if (code >= start && code <= end) {
                zh++;
                matched = true;
                break;
            }
        }
        if (matched) continue;

        // 检查维吾尔语
        for (const [start, end] of uyghurRanges) {
            if (code >= start && code <= end) {
                ug++;
                matched = true;
                break;
            }
        }
        if (matched) continue;

        // 检查英语
        for (const [start, end] of englishRanges) {
            if (code >= start && code <= end) {
                en++;
                matched = true;
                break;
            }
        }
        if (matched) continue;

        // 忽略空格
        if (char.trim() !== '') {
            other++;
        }
    }

    // 构建语言代码
    const langParts = [];
    if (zh > 0) langParts.push('zh');
    if (ug > 0) langParts.push('ug');
    if (en > 0) langParts.push('en');
    const langCode = langParts.join('+') || 'other';

    // 构建描述文本
    let description = '';
    if (langParts.length === 1) {
        description =
            langParts[0] === 'zh' ? '中文' :
            langParts[0] === 'ug' ? '维吾尔文' :
            '英文';
    } else if (langParts.length > 1) {
        const parts = [];
        if (zh > 0) parts.push('中文');
        if (ug > 0) parts.push('维吾尔文');
        if (en > 0) parts.push('英文');
        description = parts.join('和');
    } else {
        description = '其他语言';
    }


    let dir = 'ltr'; 
    if (ug > 0) {
        if (ug > en && ug > zh) { 
            dir = 'rtl';
        } else if (en === 0 && zh === 0) { 
            dir = 'rtl';
        }
    }


    return {
        lang: langCode,
        text: description,
        dir,
        counts: { zh, ug, en, other }
    };
}
// 音色名称方向判断
export const textDir=(text:any)=>{
  let {dir}=detectLanguage(text)
  return dir
}