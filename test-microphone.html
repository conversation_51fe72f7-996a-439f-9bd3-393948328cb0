<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>麦克风测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .button:hover {
            background: #40a9ff;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>麦克风兼容性测试</h1>
        <p>此页面用于测试不同浏览器（特别是Edge）的麦克风访问兼容性</p>
        
        <div class="test-section">
            <h3>浏览器信息</h3>
            <div id="browserInfo" class="result info"></div>
        </div>

        <div class="test-section">
            <h3>基础麦克风测试</h3>
            <button class="button" onclick="testBasicMicrophone()">测试基础麦克风访问</button>
            <div id="basicResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>渐进式约束测试</h3>
            <button class="button" onclick="testProgressiveConstraints()">测试渐进式约束</button>
            <div id="progressiveResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>设备枚举测试</h3>
            <button class="button" onclick="testDeviceEnumeration()">枚举音频设备</button>
            <div id="deviceResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>高级诊断</h3>
            <button class="button" onclick="runAdvancedDiagnostics()">运行高级诊断</button>
            <button class="button" onclick="testPermissionStatus()">检查权限状态</button>
            <button class="button" onclick="testWithTimeout()">超时重试测试</button>
            <div id="diagnosticsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>完整诊断报告</h3>
            <button class="button" onclick="runFullDiagnostics()">生成完整诊断报告</button>
            <div id="fullDiagnosticsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>解决方案建议</h3>
            <div class="result info">
                <strong>NotReadableError 常见解决方案：</strong><br>
                1. 关闭所有可能使用麦克风的应用程序（QQ、微信、钉钉、腾讯会议等）<br>
                2. 重启浏览器<br>
                3. 检查Windows隐私设置中的麦克风权限<br>
                4. 在设备管理器中禁用并重新启用麦克风设备<br>
                5. 重启电脑<br>
                6. 尝试使用Chrome浏览器<br>
                7. 检查是否有杀毒软件阻止麦克风访问<br><br>
                <strong>立即尝试的快速解决方案：</strong><br>
                • 按 Ctrl+Shift+Delete 清除浏览器数据<br>
                • 在浏览器地址栏输入 chrome://settings/content/microphone 检查权限<br>
                • 任务管理器中结束所有音频相关进程
            </div>
        </div>
    </div>

    <script>
        // 检测浏览器信息
        function getBrowserInfo() {
            const userAgent = navigator.userAgent;
            const isEdge = /Edg/.test(userAgent);
            const isChrome = /Chrome/.test(userAgent) && !/Edg/.test(userAgent);
            const isFirefox = /Firefox/.test(userAgent);
            const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
            
            return {
                isEdge,
                isChrome,
                isFirefox,
                isSafari,
                name: isEdge ? 'Edge' : isChrome ? 'Chrome' : isFirefox ? 'Firefox' : isSafari ? 'Safari' : 'Unknown',
                userAgent
            };
        }

        // 获取音频约束
        function getAudioConstraints() {
            const browserInfo = getBrowserInfo();
            
            if (browserInfo.isEdge) {
                return [
                    { audio: true },
                    {
                        audio: {
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    },
                    {
                        audio: {
                            sampleRate: 44100,
                            echoCancellation: true,
                            noiseSuppression: true,
                            autoGainControl: true
                        }
                    }
                ];
            }
            
            return [
                {
                    audio: {
                        channelCount: 1,
                        sampleRate: 24000,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                },
                {
                    audio: {
                        channelCount: 1,
                        sampleRate: 44100,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                },
                {
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                },
                { audio: true }
            ];
        }

        // 显示浏览器信息
        function displayBrowserInfo() {
            const info = getBrowserInfo();
            const element = document.getElementById('browserInfo');
            element.textContent = `浏览器: ${info.name}\nUser Agent: ${info.userAgent}`;
        }

        // 测试基础麦克风访问
        async function testBasicMicrophone() {
            const resultElement = document.getElementById('basicResult');
            resultElement.textContent = '正在测试...';
            resultElement.className = 'result info';

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                
                resultElement.textContent = '✅ 基础麦克风访问成功';
                resultElement.className = 'result success';
            } catch (error) {
                resultElement.textContent = `❌ 基础麦克风访问失败:\n错误类型: ${error.name}\n错误信息: ${error.message}`;
                resultElement.className = 'result error';
            }
        }

        // 测试渐进式约束
        async function testProgressiveConstraints() {
            const resultElement = document.getElementById('progressiveResult');
            resultElement.textContent = '正在测试...';
            resultElement.className = 'result info';

            const constraints = getAudioConstraints();
            let results = [];

            for (let i = 0; i < constraints.length; i++) {
                const constraint = constraints[i];
                try {
                    const stream = await navigator.mediaDevices.getUserMedia(constraint);
                    stream.getTracks().forEach(track => track.stop());
                    
                    results.push(`✅ 约束 ${i + 1} 成功: ${JSON.stringify(constraint, null, 2)}`);
                    break; // 成功后停止测试
                } catch (error) {
                    results.push(`❌ 约束 ${i + 1} 失败 (${error.name}): ${JSON.stringify(constraint, null, 2)}`);
                }
            }

            resultElement.textContent = results.join('\n\n');
            resultElement.className = results.some(r => r.includes('✅')) ? 'result success' : 'result error';
        }

        // 测试设备枚举
        async function testDeviceEnumeration() {
            const resultElement = document.getElementById('deviceResult');
            resultElement.textContent = '正在枚举设备...';
            resultElement.className = 'result info';

            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                
                let result = `找到 ${audioInputs.length} 个音频输入设备:\n\n`;
                audioInputs.forEach((device, index) => {
                    result += `设备 ${index + 1}:\n`;
                    result += `  ID: ${device.deviceId}\n`;
                    result += `  标签: ${device.label || '(需要权限才能显示)'}\n`;
                    result += `  组ID: ${device.groupId}\n\n`;
                });

                resultElement.textContent = result;
                resultElement.className = 'result success';
            } catch (error) {
                resultElement.textContent = `❌ 设备枚举失败:\n错误类型: ${error.name}\n错误信息: ${error.message}`;
                resultElement.className = 'result error';
            }
        }

        // 高级诊断
        async function runAdvancedDiagnostics() {
            const resultElement = document.getElementById('diagnosticsResult');
            resultElement.textContent = '正在运行高级诊断...';
            resultElement.className = 'result info';

            let diagnostics = [];

            // 1. 检查MediaDevices支持
            diagnostics.push(`MediaDevices支持: ${!!navigator.mediaDevices}`);
            diagnostics.push(`getUserMedia支持: ${!!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)}`);

            // 2. 检查HTTPS状态
            diagnostics.push(`HTTPS状态: ${location.protocol === 'https:' ? '是' : '否'}`);
            diagnostics.push(`本地文件: ${location.protocol === 'file:' ? '是' : '否'}`);

            // 3. 尝试获取设备权限状态
            try {
                if (navigator.permissions) {
                    const micPermission = await navigator.permissions.query({ name: 'microphone' });
                    diagnostics.push(`麦克风权限状态: ${micPermission.state}`);
                } else {
                    diagnostics.push(`权限API: 不支持`);
                }
            } catch (e) {
                diagnostics.push(`权限检查失败: ${e.message}`);
            }

            // 4. 检查音频上下文
            try {
                const AudioContext = window.AudioContext || window.webkitAudioContext;
                if (AudioContext) {
                    const audioContext = new AudioContext();
                    diagnostics.push(`AudioContext状态: ${audioContext.state}`);
                    diagnostics.push(`采样率: ${audioContext.sampleRate}Hz`);
                    await audioContext.close();
                } else {
                    diagnostics.push(`AudioContext: 不支持`);
                }
            } catch (e) {
                diagnostics.push(`AudioContext测试失败: ${e.message}`);
            }

            // 5. 检查MediaRecorder支持
            if (window.MediaRecorder) {
                const formats = ['audio/wav', 'audio/webm', 'audio/mp4', 'audio/ogg'];
                const supportedFormats = formats.filter(format => MediaRecorder.isTypeSupported(format));
                diagnostics.push(`支持的录音格式: ${supportedFormats.join(', ') || '无'}`);
            } else {
                diagnostics.push(`MediaRecorder: 不支持`);
            }

            resultElement.textContent = diagnostics.join('\n');
            resultElement.className = 'result info';
        }

        // 检查权限状态
        async function testPermissionStatus() {
            const resultElement = document.getElementById('diagnosticsResult');

            try {
                if (!navigator.permissions) {
                    resultElement.textContent = '浏览器不支持权限API';
                    resultElement.className = 'result error';
                    return;
                }

                const micPermission = await navigator.permissions.query({ name: 'microphone' });

                let result = `当前权限状态: ${micPermission.state}\n\n`;

                switch (micPermission.state) {
                    case 'granted':
                        result += '✅ 权限已授予，但仍然出现NotReadableError，这通常表示硬件层面的问题';
                        break;
                    case 'denied':
                        result += '❌ 权限被拒绝，请在浏览器设置中允许麦克风访问';
                        break;
                    case 'prompt':
                        result += '⚠️ 需要用户授权，请尝试访问麦克风';
                        break;
                }

                resultElement.textContent = result;
                resultElement.className = micPermission.state === 'granted' ? 'result info' : 'result error';

            } catch (error) {
                resultElement.textContent = `权限检查失败: ${error.message}`;
                resultElement.className = 'result error';
            }
        }

        // 超时重试测试
        async function testWithTimeout() {
            const resultElement = document.getElementById('diagnosticsResult');
            resultElement.textContent = '正在进行超时重试测试...';
            resultElement.className = 'result info';

            const timeoutPromise = (ms) => new Promise((_, reject) =>
                setTimeout(() => reject(new Error('超时')), ms)
            );

            let results = [];

            // 测试不同的超时时间
            const timeouts = [1000, 3000, 5000];

            for (const timeout of timeouts) {
                try {
                    const getUserMediaPromise = navigator.mediaDevices.getUserMedia({ audio: true });
                    const stream = await Promise.race([getUserMediaPromise, timeoutPromise(timeout)]);

                    stream.getTracks().forEach(track => track.stop());
                    results.push(`✅ ${timeout}ms内成功获取音频流`);
                    break;
                } catch (error) {
                    if (error.message === '超时') {
                        results.push(`⏱️ ${timeout}ms超时`);
                    } else {
                        results.push(`❌ ${timeout}ms内失败: ${error.name} - ${error.message}`);
                    }
                }

                // 等待一秒再尝试下一个
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            resultElement.textContent = results.join('\n');
            resultElement.className = results.some(r => r.includes('✅')) ? 'result success' : 'result error';
        }

        // 运行完整诊断
        async function runFullDiagnostics() {
            const resultElement = document.getElementById('fullDiagnosticsResult');
            resultElement.textContent = '正在运行完整诊断...';
            resultElement.className = 'result info';

            try {
                const diagnostics = await runMicrophoneDiagnostics();

                let report = `=== 麦克风诊断报告 ===\n\n`;
                report += `浏览器: ${diagnostics.browserInfo.name}\n`;
                report += `整体状态: ${diagnostics.overallStatus}\n\n`;

                report += `=== 详细检查结果 ===\n`;
                diagnostics.results.forEach(result => {
                    const statusIcon = {
                        'success': '✅',
                        'warning': '⚠️',
                        'error': '❌',
                        'info': 'ℹ️'
                    }[result.status];

                    report += `${statusIcon} ${result.category}: ${result.message}\n`;
                    if (result.details) {
                        report += `   详情: ${result.details}\n`;
                    }
                    if (result.suggestions) {
                        report += `   建议: ${result.suggestions.join(', ')}\n`;
                    }
                    report += '\n';
                });

                report += `=== 推荐解决方案 ===\n`;
                diagnostics.recommendations.forEach(rec => {
                    report += `${rec}\n`;
                });

                resultElement.textContent = report;
                resultElement.className = diagnostics.overallStatus === 'healthy' ? 'result success' :
                                         diagnostics.overallStatus === 'warning' ? 'result info' : 'result error';

            } catch (error) {
                resultElement.textContent = `诊断失败: ${error.message}`;
                resultElement.className = 'result error';
            }
        }

        // 简化版诊断函数（用于测试页面）
        async function runMicrophoneDiagnostics() {
            const browserInfo = getBrowserInfo();
            const results = [];

            // 基础API检查
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                results.push({
                    category: 'API支持',
                    status: 'error',
                    message: 'MediaDevices API 不支持',
                    suggestions: ['使用现代浏览器']
                });
            } else {
                results.push({
                    category: 'API支持',
                    status: 'success',
                    message: '基础API支持正常'
                });
            }

            // HTTPS检查
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.protocol === 'file:';
            results.push({
                category: 'HTTPS状态',
                status: isSecure ? 'success' : 'error',
                message: isSecure ? '安全上下文正常' : '需要HTTPS环境',
                suggestions: isSecure ? [] : ['使用HTTPS协议']
            });

            // 权限检查
            try {
                if (navigator.permissions) {
                    const permission = await navigator.permissions.query({ name: 'microphone' });
                    results.push({
                        category: '权限检查',
                        status: permission.state === 'granted' ? 'success' : permission.state === 'denied' ? 'error' : 'warning',
                        message: `权限状态: ${permission.state}`,
                        suggestions: permission.state === 'denied' ? ['在浏览器设置中允许麦克风访问'] : []
                    });
                }
            } catch (e) {
                results.push({
                    category: '权限检查',
                    status: 'warning',
                    message: '无法检查权限状态'
                });
            }

            // 设备检查
            try {
                const devices = await navigator.mediaDevices.enumerateDevices();
                const audioInputs = devices.filter(device => device.kind === 'audioinput');
                results.push({
                    category: '音频设备',
                    status: audioInputs.length > 0 ? 'success' : 'error',
                    message: `检测到 ${audioInputs.length} 个音频输入设备`,
                    suggestions: audioInputs.length === 0 ? ['连接麦克风设备'] : []
                });
            } catch (e) {
                results.push({
                    category: '音频设备',
                    status: 'error',
                    message: '设备枚举失败',
                    details: e.message
                });
            }

            // 音频访问测试
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                results.push({
                    category: '音频访问测试',
                    status: 'success',
                    message: '基础音频访问成功'
                });
            } catch (error) {
                const suggestions = error.name === 'NotReadableError' ?
                    ['关闭其他音频应用', '重启浏览器', '检查驱动程序'] :
                    ['检查权限设置'];

                results.push({
                    category: '音频访问测试',
                    status: 'error',
                    message: `音频访问失败: ${error.name}`,
                    details: error.message,
                    suggestions
                });
            }

            const errorCount = results.filter(r => r.status === 'error').length;
            const warningCount = results.filter(r => r.status === 'warning').length;

            const overallStatus = errorCount > 0 ? 'critical' : warningCount > 0 ? 'warning' : 'healthy';

            const recommendations = [];
            const hasNotReadableError = results.some(r => r.details?.includes('NotReadableError'));

            if (hasNotReadableError) {
                recommendations.push('🔧 NotReadableError 解决方案：');
                recommendations.push('1. 关闭所有音频应用程序');
                recommendations.push('2. 重启浏览器');
                recommendations.push('3. 检查音频驱动程序');
                if (browserInfo.isEdge) {
                    recommendations.push('4. 尝试使用Chrome浏览器');
                }
            }

            return {
                browserInfo,
                results,
                overallStatus,
                recommendations
            };
        }

        // 页面加载时显示浏览器信息
        window.onload = function() {
            displayBrowserInfo();
        };
    </script>
</body>
</html>
