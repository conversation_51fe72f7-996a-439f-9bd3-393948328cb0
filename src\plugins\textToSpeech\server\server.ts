import { getInject } from "@/utils/getInject.ts";
import {processStreamResponse} from 'mstf-kit'
export const textToSpeechSerApi = () => {
    const { api } = getInject(); // 可以括号里获取封装好的方法数据 router / ossUploader 等
    // 音色列表
    const getclone_list = async () => {
        return await api.request.main.get('/clone/clone_list');
    }
        // 语音合成
    const synthesize1 = async (params: {
        clone_id: number;
        text: string;
        speed: number;
    }) => {
        return await api.request.main.post('clone/text_to_voice', params);
    }
    // 语音合成
    const synthesize=async (params: {clone_id: number;text: string;speed: number;})=>{
        const result= await api.streamFetchRequest.main.post('/clone/text_to_voice',{...params,stream:true})
        const reader:any= result.body?.getReader
         processStreamResponse(reader,{
            returnBlob:true,
            onAudioData:(blob)=>{
                console.log('收到的音频数据块',blob);
            },
            onComplete:(blob)=>{
                if(blob) console.log('完整大小',blob.size)
            }
        })
        console.log('语音合成',result);
        

    }
    // 获取参考文本
    const get_reference_text = async (lang: string) => {
        return await api.request.main.get(`clone/reference_text?lang=${lang}`);
    }
    // 上传音频克隆音色
    const upload_voice = async (params: { name: string, audio_file: File }) => {
        const formData = new FormData();
        formData.append('audio', params.audio_file);
        formData.append('name', params.name);
        return await api.request.main.post("clone/create", formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
    }
    // 删除音色
    const delete_voice=async(clone_id:number)=>{
        return await api.request.main.delete('clone/delete',{params:{clone_id}})
    }
    return {
        getclone_list,
        synthesize,
        upload_voice,
        get_reference_text,
        delete_voice
    };

};
