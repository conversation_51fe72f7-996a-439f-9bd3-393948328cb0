import {type RouteRecordRaw} from 'vue-router';
import {routers} from "@/utils/routerHelper.ts";

const textToSpeechRoutes: RouteRecordRaw[] = [
    {
          path: '/textToSpeech2',
          name: 'TextToSpeech2',
          component: ()=> import('./index.vue'),
          meta: {
            title: 'textToSpeech 插件',
            requiresAuth: false,
            ivViewMenu: false
          }
    }
];

let isRoutesAdded = false;

const initModule = () => {
    if (import.meta.env.MODE === 'production' && !isRoutesAdded) {
        const router = routers();
        textToSpeechRoutes.forEach(route => {
            router.addRoute("Layout", route);
        });
        isRoutesAdded = true;
    }
};

export {
    textToSpeechRoutes as default,
    initModule
};
